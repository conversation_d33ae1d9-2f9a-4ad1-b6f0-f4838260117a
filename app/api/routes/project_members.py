from typing import List, Optional
from app.api.deps import get_current_user_from_state
from app.api.repository.user import is_user_authed
from app.core.logging import get_logger
from app.services.llm_service import call_llm
from app.services.prompts import generate_team_introduction_prompt
from fastapi import APIRouter, status ,Request
from datetime import datetime
from app.models.project_members import ProjectMember
# from app.models.model_config import ModelConfig
from app.api.schemas.project_members import ProjectMemberCreate, ProjectMemberResponse, ProjectMemberUpdate, TeamIntroductionRequest, TeamIntroductionResponse, TeamMemberInfo
from app.utils.utils import send_data, ResponseModel
from app.api.repository.user_default_model import get_user_model
from app.models.organization_model_use import UseCase
from app.api.schemas.role import InsetRole
# 获取logger实例
logger = get_logger(__name__)

router = APIRouter()

# 管理员接口
# 管理员获取所有项目成员
@router.get("/list",response_model=ResponseModel[List[ProjectMemberResponse]])
async def read_project_members(
    request: Request,
    is_deleted: Optional[int] = 0
):
    """获取项目成员列表（支持用户隔离）"""
    current_user = get_current_user_from_state(request)

    query = ProjectMember.all().prefetch_related("create_by", "create_by__role", "create_by__organization")

    # 根据用户角色进行过滤
    if current_user.role.identifier == InsetRole.SUPER_ADMIN:
        # 超级管理员可以看到所有数据（包括历史数据）
        members = await query.filter(is_deleted=is_deleted).all()
    elif current_user.role.identifier == InsetRole.ADMIN:
        # 机构管理员可以看到本机构用户创建的数据
        members = await query.filter(
            is_deleted=is_deleted,
            create_by__organization_id=current_user.organization.id
        ).all()
    else:
        # 普通用户只能看到自己创建的数据
        members = await query.filter(
            is_deleted=is_deleted,
            create_by_id=current_user.id
        ).all()

    result = [ProjectMemberResponse.model_validate(member, from_attributes=True) for member in members]
    return send_data(True, result)

# 获取指定项目成员
@router.get("/{member_id}", response_model=ResponseModel[ProjectMemberResponse])
async def read_project_member(
    member_id: str,
    request: Request
):
    """获取特定项目成员详情（支持权限验证）"""
    current_user = get_current_user_from_state(request)
    member = await ProjectMember.filter(id=member_id).first()
    if member is None:
        return send_data(False, None, "项目成员不存在")
    if not is_user_authed(
            operator=current_user.id,
            resource_belong_user=member.create_by
    ):
        return send_data(False, None, "无权访问此项目成员")
    return send_data(True, ProjectMemberResponse.model_validate(member, from_attributes=True))


# 创建项目成员
@router.post("", response_model=ResponseModel[ProjectMemberResponse], status_code=status.HTTP_201_CREATED)
async def create_project_member(
    member_in: ProjectMemberCreate,
    request: Request
):
    """创建新的项目成员（自动关联当前用户）"""
    current_user = get_current_user_from_state(request)

    try:
        # 创建项目成员
        member = ProjectMember(
          name = member_in.name,
          title = member_in.title,
          representative_works = member_in.representative_works,
          organization = member_in.organization,
          introduction = member_in.introduction,
          education = member_in.education,
          create_by_id = current_user.id  # 自动关联当前用户
        )
        await member.save()
        
        return send_data(True, ProjectMemberResponse.model_validate(member, from_attributes=True))
    except Exception as e:
        return send_data(False, None, f"创建项目成员失败: {str(e)}")


# 更新项目成员
@router.put("/{member_id}", response_model=ResponseModel[ProjectMemberResponse])
async def update_project_member(
    member_id: str,
    member_in: ProjectMemberUpdate,
    request: Request
):
    """更新项目成员（支持权限验证）"""
    current_user = get_current_user_from_state(request)
    member = await ProjectMember.filter(id=member_id).first()
    if member is None:
        return send_data(False, None, "项目成员不存在")

    if not is_user_authed(
            operator=current_user.id,
            resource_belong_user=member.create_by
    ):
        return send_data(False, None, "无权修改此项目成员")
    try:
        # 处理更新字段
        update_data = member_in.dict(exclude_unset=True)
        
        # 如果更新了is_deleted字段为1，设置deleted_at时间
        if "is_deleted" in update_data and update_data["is_deleted"] == 1:
            update_data["deleted_at"] = datetime.now()
        
        # 更新其他常规字段
        for key, value in update_data.items():
            setattr(member, key, value)
        
        await member.save()
        return send_data(True, ProjectMemberResponse.model_validate(member, from_attributes=True))
    except Exception as e:
        return send_data(False, None, f"更新项目成员失败: {str(e)}")


# 软删除项目成员
@router.delete("/{member_id}", response_model=ResponseModel[ProjectMemberResponse])
async def soft_delete_project_member(
    member_id: str,
    request: Request,
):
    """获取项目成员列表（支持用户隔离）"""
    current_user = get_current_user_from_state(request)

    member = await ProjectMember.filter(id=member_id).first()
    if member is None:
        return send_data(False, None, "项目成员不存在")

    if not is_user_authed(
            operator=current_user.id,
            resource_belong_user=member.create_by
    ):
        return send_data(False, None, "无权删除此项目成员")

    try:
        # 标记为已删除
        member.is_deleted = 1
        member.deleted_at = datetime.now()
        await member.save()
        
        return send_data(True, ProjectMemberResponse.model_validate(member, from_attributes=True))
    except Exception as e:
        return send_data(False, None, f"删除项目成员失败: {str(e)}")
    

    
# 生成团队介绍接口
@router.post("/introduction", response_model=ResponseModel[TeamIntroductionResponse])
async def generate_team_introduction(
    team_request: TeamIntroductionRequest,
    request: Request
):
    """根据团队成员ID及项目研究课题名称生成团队成员介绍（通过LLM）
    
    请求体JSON格式：
    {
        "member_ids": ["id1", "id2", "id3"],
        "project_configs_name": "项目研究课题名称",
        "model_config_id": "可选的模型配置ID"
    }
    """
    # 获取当前用户
    current_user =  get_current_user_from_state(request)
    
    # 1. 验证成员ID数组
    if not team_request.member_ids:
        return send_data(False, None, "请提供有效的成员ID")
    
    # 2. 查询指定的多个团队成员（支持用户权限过滤）
    team_members = []
    members_text = []

    # 根据用户角色过滤成员
    query = ProjectMember.filter(
        id__in=team_request.member_ids,
        is_deleted=0
    ).prefetch_related("create_by", "create_by__role", "create_by__organization")

    if current_user.role.identifier == InsetRole.SUPER_ADMIN:
        # 超级管理员可以使用所有成员
        members = await query.all()
    elif current_user.role.identifier == InsetRole.ADMIN:
        # 机构管理员只能使用本机构用户创建的成员
        members = await query.filter(create_by__organization_id=current_user.organization.id).all()
    else:
        # 普通用户只能使用自己创建的成员
        members = await query.filter(create_by_id=current_user.id).all()

    # 检查是否找到了成员
    if not members:
        return send_data(False, None, f"未找到指定的成员或无权使用这些成员")
    
    # 查询模型配置信息
    model_config = await get_user_model(
        current_user=current_user,
        use_case=UseCase.PROJECT_CONFIG_NEED.value
    )
    # if team_request.model_config_id:
    #     # 如果指定了model_config_id，使用指定的模型配置
    #     model_config = await ModelConfig.filter(id=team_request.model_config_id, is_deleted=False).first()
    #     if model_config is None:
    #         return send_data(False, None, "未找到指定的模型配置")
    # else:
    #     # 否则使用默认模型配置
    #     model_config = await ModelConfig.filter(is_deleted=False, user=current_user, is_active=True).first()
    #     if model_config is None:
    #         return send_data(False, None, "未找到有效的模型配置")
    
    
    # 处理找到的成员信息
    for member in members:
        member_info = TeamMemberInfo(
            name=member.name,
            title=member.title,
            organization=member.organization,
            introduction=member.introduction,
            education=member.education,
            representative_works=member.representative_works
        )
        team_members.append(member_info)
        
        # 构建成员文本描述
        member_text = f"成员姓名：{member.name}, 职称：{member.title}"
        if member.organization:
            member_text += f", 工作单位：{member.organization}"
        if member.introduction:
            member_text += f", 简介：{member.introduction}"
        if member.education:
            member_text += f", 学历：{member.education}"
        #member_text += f", 代表作：{member.representative_works}"
        members_text.append(member_text)
    
    # 检查是否成功获取到成员信息
    if not team_members:
        return send_data(False, None, "未找到有效的团队成员信息")
    
    # 3. 使用prompts.py中的函数构建提示词
    formatted_members_info = "\n".join([f"- {text}" for text in members_text])
    
    # 根据成员数量调整标题
    if len(team_members) == 1:
        prompt_title = f"成员介绍（{team_members[0].name}）"
    else:
        member_names = ", ".join([member_info.name for member_info in team_members])
        prompt_title = f"多位成员介绍（{member_names}）"
        
    prompt = generate_team_introduction_prompt(
        members_info=formatted_members_info,
        project_configs_name=team_request.project_configs_name
    )
    logger.info(f"调用LLM服务，prompt模型：{prompt}")
    # 从model_config获取模型配置
    model = model_config.model_name
    api_key = model_config.api_key
    api_url = model_config.api_url
   
    # 4. 调用LLM服务
    try:
        introduction = await call_llm(messages=prompt, flag="team_introduction", model=model, apiKey=api_key, apiUrl=api_url)
        if not introduction:
            return send_data(False, None, "生成团队成员介绍失败，请稍后重试")
        
        # 5. 构建响应数据
        
        result = TeamIntroductionResponse(
            team_introduction=introduction
        )
        
        return send_data(True, result)
    except Exception as e:
        return send_data(False, None, f"生成团队成员介绍时出错: {str(e)}")
