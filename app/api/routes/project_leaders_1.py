from typing import Optional, List
from fastapi import APIRouter, Depends, status, Request
from datetime import datetime
from app.models.project_leaders import ProjectLeader
from app.api.schemas.project_leaders import ProjectLeaderCreate, ProjectLeaderResponse, ProjectLeaderUpdate
from app.utils.utils import send_data, ResponseModel
from app.models.area import Area
from app.api.deps import get_current_user_from_state
from app.models.user import User
from app.api.schemas.role import InsetRole

router = APIRouter()


# 获取项目主体列表（支持用户隔离）
@router.get("/list",response_model=ResponseModel[List[ProjectLeaderResponse]])
async def read_project_leaders(
    request: Request,
    is_deleted: Optional[int] = 0
):
    """获取项目主体列表（支持用户隔离）"""
    current_user = get_current_user_from_state(request)

    query = ProjectLeader.all().prefetch_related("province", "city", "district", "create_by", "create_by__role", "create_by__organization")

    # 根据用户角色进行过滤
    if current_user.role.identifier == InsetRole.SUPER_ADMIN:
        # 超级管理员可以看到所有数据（包括历史数据）
        leaders = await query.filter(is_deleted=is_deleted).order_by("-updated_at").all()
    elif current_user.role.identifier == InsetRole.ADMIN:
        # 机构管理员可以看到本机构用户创建的数据
        leaders = await query.filter(
            is_deleted=is_deleted,
            create_by__organization_id=current_user.organization.id
        ).order_by("-updated_at").all()
    else:
        # 普通用户只能看到自己创建的数据
        leaders = await query.filter(
            is_deleted=is_deleted,
            create_by_id=current_user.id
        ).order_by("-updated_at").all()

    result = [ProjectLeaderResponse.model_validate(leader, from_attributes=True) for leader in leaders]
    return send_data(True, result)


# 获取指定项目主体（支持权限验证）
@router.get("/{leader_id}", response_model=ResponseModel[ProjectLeaderResponse])
async def read_project_leader(
    leader_id: str,
    request: Request
):
    """获取特定项目主体详情（支持权限验证）"""
    current_user = get_current_user_from_state(request)

    leader = await ProjectLeader.filter(id=leader_id).prefetch_related("province", "city", "district", "create_by", "create_by__role", "create_by__organization").first()
    if leader is None:
        return send_data(False, None, "项目主体不存在")

    # 权限验证
    if current_user.role.identifier == InsetRole.SUPER_ADMIN:
        # 超级管理员可以访问所有数据
        pass
    elif current_user.role.identifier == InsetRole.ADMIN:
        # 机构管理员只能访问本机构用户创建的数据
        if leader.create_by and leader.create_by.organization_id != current_user.organization.id:
            return send_data(False, None, "无权访问此项目主体")
    else:
        # 普通用户只能访问自己创建的数据
        if not leader.create_by or leader.create_by.id != current_user.id:
            return send_data(False, None, "无权访问此项目主体")

    return send_data(True, ProjectLeaderResponse.model_validate(leader, from_attributes=True))


# 管理员创建项目主体
@router.post("", response_model=ResponseModel[ProjectLeaderResponse], status_code=status.HTTP_201_CREATED)
async def create_project_leader(
    leader: ProjectLeaderCreate,
    request: Request
):
    """创建新的项目主体（管理员接口）"""
    current_user = get_current_user_from_state(request)
    try:
        # 校验统一社会信用代码长度
        if len(leader.credit_code) != 18:
            return send_data(False, None, "统一社会信用代码必须为18位")
      
        # 创建项目主体
        province = None
        if leader.province_id:
            province = await Area.get(id=leader.province_id)
            if province is None:
                return send_data(False, None, "省份不存在")
        city = None
        if leader.city_id:
            city = await Area.get(id=leader.city_id)
            if city is None:
                return send_data(False, None, "城市不存在")
        district = None
        if leader.district_id:
            district = await Area.get(id=leader.district_id)
            if district is None:
                return send_data(False, None, "区县不存在")
        leader = await ProjectLeader.create(
            name = leader.name,
            credit_code = leader.credit_code,
            institution_type = leader.institution_type,
            province = province,
            city = city,
            district = district,
            address = leader.address,
            website = leader.website,
            founded_date = leader.founded_date,
            create_by_id=current_user.id,
            # patent_count: Optional[int] = Field(None, description="专利数量")
            related_projects = leader.related_projects
        )
        # await leader.save()
        
        return send_data(True, ProjectLeaderResponse.model_validate(leader, from_attributes=True))
    except Exception as e:
        return send_data(False, None, f"创建项目主体失败: {str(e)}")


# 更新项目主体（支持权限验证）
@router.put("/{leader_id}", response_model=ResponseModel[ProjectLeaderResponse])
async def update_project_leader(
    leader_id: str,
    leader_in: ProjectLeaderUpdate,
    request: Request
):
    """更新项目主体（支持权限验证）"""
    current_user = get_current_user_from_state(request)

    leader = await ProjectLeader.filter(id=leader_id).prefetch_related('province', 'city', 'district', 'create_by', 'create_by__role', 'create_by__organization').first()
    if leader is None:
        return send_data(False, None, "项目主体不存在")

    # 权限验证
    if current_user.role.identifier == InsetRole.SUPER_ADMIN:
        # 超级管理员可以修改所有数据
        pass
    elif current_user.role.identifier == InsetRole.ADMIN:
        # 机构管理员只能修改本机构用户创建的数据
        if leader.create_by and leader.create_by.organization_id != current_user.organization.id:
            return send_data(False, None, "无权修改此项目主体")
    else:
        # 普通用户只能修改自己创建的数据
        if not leader.create_by or leader.create_by.id != current_user.id:
            return send_data(False, None, "无权修改此项目主体")
    
    try:
        # 处理更新字段
        update_data = leader_in.dict(exclude_unset=True)
        
        # 如果更新了统一社会信用代码，需要校验长度
        if "credit_code" in update_data and update_data["credit_code"]:
            credit_code = update_data["credit_code"]
            
            # 校验社会信用代码长度
            if len(credit_code) != 18:
                return send_data(False, None, "统一社会信用代码必须为18位")
                
        
        # 如果更新了is_deleted字段为1，设置deleted_at时间
        if "is_deleted" in update_data and update_data["is_deleted"] == 1:
            update_data["deleted_at"] = datetime.now()
        if "province_id" in update_data and update_data["province_id"]:
            province_id = update_data.pop("province_id")
            update_data["province"] = await Area.filter(id=province_id).first()
        if "city_id" in update_data and update_data["city_id"]:
            city_id = update_data.pop("city_id")
            update_data["city"] = await Area.filter(id=city_id).first()
        if "district_id" in update_data and update_data["district_id"]:
            district_id = update_data.pop("district_id")
            update_data["district"] = await Area.filter(id=district_id).first()
        
        # 更新其他常规字段
        for key, value in update_data.items():
            setattr(leader, key, value)
        
        await leader.save()
        return send_data(True, ProjectLeaderResponse.model_validate(leader))
    except Exception as e:
        return send_data(False, None, f"更新项目主体失败: {str(e)}")


# 软删除项目主体（支持权限验证）
@router.delete("/{leader_id}", response_model=ResponseModel[ProjectLeaderResponse])
async def soft_delete_project_leader(
    leader_id: str,
    request: Request
):
    """软删除项目主体（支持权限验证）"""
    current_user = get_current_user_from_state(request)

    leader = await ProjectLeader.filter(id=leader_id).prefetch_related("province", "city", "district", "create_by", "create_by__role", "create_by__organization").first()
    if leader is None:
        return send_data(False, None, "项目主体不存在")

    # 权限验证
    if current_user.role.identifier == InsetRole.SUPER_ADMIN:
        # 超级管理员可以删除所有数据
        pass
    elif current_user.role.identifier == InsetRole.ADMIN:
        # 机构管理员只能删除本机构用户创建的数据
        if leader.create_by and leader.create_by.organization_id != current_user.organization.id:
            return send_data(False, None, "无权删除此项目主体")
    else:
        # 普通用户只能删除自己创建的数据
        if not leader.create_by or leader.create_by.id != current_user.id:
            return send_data(False, None, "无权删除此项目主体")

    try:
        # 标记为已删除
        leader.is_deleted = 1
        leader.deleted_at = datetime.now()
        await leader.save()

        return send_data(True, ProjectLeaderResponse.model_validate(leader, from_attributes=True))
    except Exception as e:
        return send_data(False, None, f"删除项目主体失败: {str(e)}")


