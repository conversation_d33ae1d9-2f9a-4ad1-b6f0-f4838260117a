#!/usr/bin/env python3
"""
测试用户隔离功能的脚本
用于验证材料主题和团队成员的用户隔离代码结构是否正确
"""

import sys
import os
import inspect

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_model_structure():
    """测试模型结构"""
    print("=== 测试模型结构 ===")

    try:
        from app.models.project_members import ProjectMember
        print("✅ ProjectMember模型导入成功")

        # 检查是否有user字段
        if hasattr(ProjectMember, 'user'):
            print("✅ ProjectMember模型包含user字段")
        else:
            print("❌ ProjectMember模型缺少user字段")

    except Exception as e:
        print(f"❌ ProjectMember模型导入失败: {e}")

    try:
        from app.models.project_leaders import ProjectLeader
        print("✅ ProjectLeader模型导入成功")

        # 检查是否有user字段
        if hasattr(ProjectLeader, 'user'):
            print("✅ ProjectLeader模型包含user字段")
        else:
            print("❌ ProjectLeader模型缺少user字段")

    except Exception as e:
        print(f"❌ ProjectLeader模型导入失败: {e}")

def test_api_structure():
    """测试API结构"""
    print("\n=== 测试API结构 ===")

    try:
        from app.api.routes.project_members import router as member_router
        print("✅ ProjectMember路由导入成功")

        # 检查路由函数
        routes = [rule.path for rule in member_router.routes]
        print(f"✅ ProjectMember路由包含: {routes}")

    except Exception as e:
        print(f"❌ ProjectMember路由导入失败: {e}")

    try:
        from app.api.routes.project_leaders import router as leader_router
        print("✅ ProjectLeader路由导入成功")

        # 检查路由函数
        routes = [rule.path for rule in leader_router.routes]
        print(f"✅ ProjectLeader路由包含: {routes}")

    except Exception as e:
        print(f"❌ ProjectLeader路由导入失败: {e}")

def test_permission_imports():
    """测试权限相关导入"""
    print("\n=== 测试权限相关导入 ===")

    try:
        from app.api.schemas.role import InsetRole
        print("✅ InsetRole导入成功")
        print(f"✅ 角色类型: {list(InsetRole)}")

    except Exception as e:
        print(f"❌ InsetRole导入失败: {e}")

    try:
        from app.api.deps import get_current_user_from_state
        print("✅ get_current_user_from_state导入成功")

    except Exception as e:
        print(f"❌ get_current_user_from_state导入失败: {e}")

def check_file_modifications():
    """检查文件修改情况"""
    print("\n=== 检查文件修改情况 ===")

    files_to_check = [
        "app/models/project_members.py",
        "app/models/project_leaders.py",
        "app/api/routes/project_members.py",
        "app/api/routes/project_leaders.py"
    ]

    for file_path in files_to_check:
        if os.path.exists(file_path):
            print(f"✅ {file_path} 存在")

            # 检查文件中是否包含user相关内容
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
                if 'user' in content.lower():
                    print(f"✅ {file_path} 包含user相关内容")
                else:
                    print(f"⚠️ {file_path} 可能缺少user相关内容")
        else:
            print(f"❌ {file_path} 不存在")

def main():
    """主函数"""
    print("开始测试用户隔离功能代码结构...")

    test_model_structure()
    test_api_structure()
    test_permission_imports()
    check_file_modifications()

    print("\n测试完成！")

if __name__ == "__main__":
    main()
